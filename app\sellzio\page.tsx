"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [showKeywordPredictions, setShowKeywordPredictions] = useState(false)
  const [keywordPredictions, setKeywordPredictions] = useState<any[]>([])

  // Database keyword prediksi - sesuai docs/facet.html
  const keywordPredictionDB = {
    productKeywords: [
      "smartphone android", "sepatu sneakers", "tas selempang",
      "headphone bluetooth", "keyboard gaming", "power bank",
      "smart tv", "robot vacuum", "laptop gaming", "tablet android",
      "kamera mirrorless", "jam tangan pintar", "speaker bluetooth",
      "mouse wireless", "monitor gaming", "hard disk external",
      "memory card", "charger wireless", "case handphone", "earphone",
      "tas ransel", "tas wanita", "tas pria", "tas sekolah",
      "sepatu olahraga", "sepatu casual", "sepatu formal", "sandal",
      "baju pria", "baju wanita", "celana jeans", "kaos",
      "jaket", "sweater", "dress", "rok"
    ],
    synonyms: {
      "hp": ["handphone", "smartphone"],
      "laptop": ["notebook", "komputer"],
      "tas": ["bag", "ransel"],
      "sepatu": ["shoes", "footwear"],
      "baju": ["shirt", "clothing", "pakaian"]
    } as Record<string, string[]>,
    typoCorrections: {
      "handpone": "handphone",
      "septu": "sepatu",
      "tass": "tas",
      "bajuu": "baju",
      "labtop": "laptop"
    } as Record<string, string>,
    relatedKeywords: {
      "smartphone": ["android", "iphone", "samsung", "xiaomi"],
      "laptop": ["gaming", "asus", "lenovo", "acer"],
      "tas": ["ransel", "selempang", "wanita", "sekolah"],
      "sepatu": ["sneakers", "olahraga", "casual", "formal"]
    } as Record<string, string[]>,
    userInteractionHistory: [] as string[]
  }

  // Fungsi untuk menghasilkan prediksi keyword - sesuai docs/facet.html
  const generatePredictions = (input: string) => {
    const inputLower = input.toLowerCase().trim()
    const words = inputLower.split(' ')
    const results: any[] = []

    // 1. Tambahkan dari riwayat interaksi pengguna (maksimal 4)
    let historyCount = 0
    for (let i = 0; i < keywordPredictionDB.userInteractionHistory.length && historyCount < 4; i++) {
      const item = keywordPredictionDB.userInteractionHistory[i]
      if (item.toLowerCase().includes(inputLower)) {
        results.push({
          text: item,
          type: 'history',
          relevance: calculateRelevance(item, inputLower, 'history')
        })
        historyCount++
      }
    }

    // 2. Tambahkan dari keyword produk
    keywordPredictionDB.productKeywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(inputLower)) {
        results.push({
          text: keyword,
          type: 'product',
          relevance: calculateRelevance(keyword, inputLower, 'product')
        })
      }
    })

    // 3. Tambahkan dari related keywords
    words.forEach(word => {
      if (keywordPredictionDB.relatedKeywords[word]) {
        keywordPredictionDB.relatedKeywords[word].forEach(related => {
          results.push({
            text: related,
            type: 'related',
            relevance: calculateRelevance(related, inputLower, 'related')
          })
        })
      }
    })

    // 4. Tambahkan dari sinonim
    words.forEach(word => {
      if (keywordPredictionDB.synonyms[word]) {
        keywordPredictionDB.synonyms[word].forEach(synonym => {
          const newQuery = inputLower.replace(word, synonym)
          if (newQuery !== inputLower) {
            results.push({
              text: newQuery,
              type: 'synonym',
              relevance: calculateRelevance(newQuery, inputLower, 'synonym')
            })
          }
        })
      }
    })

    // 5. Periksa kemungkinan typo
    words.forEach(word => {
      if (keywordPredictionDB.typoCorrections[word]) {
        const corrected = inputLower.replace(word, keywordPredictionDB.typoCorrections[word])
        results.push({
          text: corrected,
          type: 'correction',
          relevance: calculateRelevance(corrected, inputLower, 'correction')
        })
      }
    })

    // Hapus duplikat dan urutkan berdasarkan relevansi
    const uniqueResults = results.filter((item, index, self) =>
      index === self.findIndex(t => t.text.toLowerCase() === item.text.toLowerCase())
    )

    return uniqueResults.sort((a, b) => b.relevance - a.relevance).slice(0, 12)
  }

  // Fungsi untuk menghitung relevansi prediksi
  const calculateRelevance = (prediction: string, input: string, type: string) => {
    let score = 0

    // Bobot berdasarkan tipe
    const typeWeights: Record<string, number> = {
      'history': 100,
      'product': 80,
      'related': 60,
      'synonym': 40,
      'correction': 20,
      'trending': 90
    }

    // Tambahkan bobot tipe
    score += typeWeights[type] || 0

    const inputWords = input.toLowerCase().split(' ')
    const predictionWords = prediction.toLowerCase().split(' ')
    const predictionLower = prediction.toLowerCase()

    // Jika prediksi dimulai dengan input, tambahkan skor lebih tinggi
    if (predictionLower.startsWith(input.toLowerCase())) {
      score += 30
    }

    // Jika prediksi berisi input persis, tambahkan skor
    if (predictionLower.includes(input.toLowerCase())) {
      score += 20
    }

    // Bobot lebih tinggi untuk setiap kata dalam input yang ada dalam prediksi
    let matchingWords = 0
    inputWords.forEach(word => {
      if (word.length > 0) {
        const hasMatch = predictionWords.some(pw => pw.includes(word))
        if (hasMatch) {
          matchingWords++
          score += word.length * 2
        }
      }
    })

    return score
  }

  // Fungsi untuk highlight text yang cocok
  const highlightMatchingText = (text: string, input: string) => {
    if (!input.trim()) return text

    const inputLower = input.toLowerCase()
    const textLower = text.toLowerCase()

    // Cari posisi yang cocok
    const index = textLower.indexOf(inputLower)
    if (index !== -1) {
      const before = text.substring(0, index)
      const match = text.substring(index, index + input.length)
      const after = text.substring(index + input.length)
      return `${before}<span class="highlighted">${match}</span>${after}`
    }

    return text
  }

  // Fungsi untuk menampilkan keyword predictions
  const showKeywordPredictionsHandler = (inputText: string) => {
    // Jika input kosong atau terlalu pendek, sembunyikan prediksi
    if (!inputText || inputText.length === 0) {
      setShowKeywordPredictions(false)
      setKeywordPredictions([])
      return
    }

    // Generate predictions
    const predictions = generatePredictions(inputText)

    // Jika tidak ada prediksi, sembunyikan kontainer
    if (predictions.length === 0) {
      setShowKeywordPredictions(false)
      setKeywordPredictions([])
      return
    }

    // Update state
    setKeywordPredictions(predictions)
    setShowKeywordPredictions(true)
  }

  // Fungsi untuk menyembunyikan keyword predictions
  const hideKeywordPredictionsHandler = () => {
    setShowKeywordPredictions(false)
    setKeywordPredictions([])
  }

  // Fungsi untuk handle click pada prediction item
  const handlePredictionClick = (prediction: any) => {
    setSearchValue(prediction.text)
    hideKeywordPredictionsHandler()

    // Tambahkan ke riwayat interaksi
    keywordPredictionDB.userInteractionHistory.unshift(prediction.text)
    if (keywordPredictionDB.userInteractionHistory.length > 20) {
      keywordPredictionDB.userInteractionHistory.pop()
    }

    console.log('Prediction clicked:', prediction.text)
  }

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // Jangan auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)

    if (value.length > 0) {
      // Tampilkan suggestions container
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')

      // Tampilkan keyword predictions jika ada input (minimal 1 karakter)
      showKeywordPredictionsHandler(value)
    } else {
      // Sembunyikan keyword predictions jika input kosong
      hideKeywordPredictionsHandler()

      if (!isSearchExpanded) {
        setShowSuggestions(false)
        document.body.classList.remove('show-suggestions')
      }
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setSearchValue('')
      setShowMoreSuggestions(false) // Reset ke bentuk tombol
      hideKeywordPredictionsHandler() // Sembunyikan keyword predictions
      document.body.classList.remove('show-suggestions')
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      setShowMoreSuggestions(false) // Pastikan bentuk tombol saat expand
      document.body.classList.add('show-suggestions')
    }
  }



  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Bisa ditambahkan logic untuk navigate atau search
    console.log('Suggestion clicked:', suggestion)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Overlay untuk expanded mode */}
        {showSuggestions && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onToggleExpanded={handleToggleExpanded}
      />

      {/* Main Content */}
      <main className="pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>
          
          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Suggestions Container - Persis seperti docs/facet.html */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          {/* Clear history option - sesuai facet.html */}
          <div className="clear-history" onClick={() => console.log('Clear history')}>
            <i className="fas fa-trash-alt" style={{ marginRight: '8px', fontSize: '12px' }}></i>
            Hapus riwayat pencarian
          </div>

          {/* Keyword suggestions di bagian atas - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */}
          {!showMoreSuggestions && (
            <div className="main-keyword-suggestions-grid">
              <div
                className="main-keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('sepatu sneakers')}
              >
                sepatu sneakers
              </div>

              <div
                className="main-keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('tas wanita')}
              >
                tas wanita
              </div>

              <div
                className="main-keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('smartphone android')}
              >
                smartphone android
              </div>

              <div
                className="main-keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('headphone bluetooth')}
              >
                headphone bluetooth
              </div>

              <div
                className="main-keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('keyboard gaming')}
              >
                keyboard gaming
              </div>

              <div
                className="main-keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('mouse wireless')}
              >
                mouse wireless
              </div>
            </div>
          )}

          {/* Keyword suggestions di bagian atas - SETELAH klik "Lihat Lainnya" = bentuk list */}
          {showMoreSuggestions && (
            <div className="main-keyword-suggestions-list">
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('sepatu sneakers')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">sepatu sneakers</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('tas wanita')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">tas wanita</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('smartphone android')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">smartphone android</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('headphone bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">headphone bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('keyboard gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">keyboard gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('mouse wireless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">mouse wireless</span>
              </div>

              {/* Extended suggestions - tambahan setelah expand */}
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('kamera mirrorless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">kamera mirrorless</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('jam tangan pintar')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">jam tangan pintar</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('speaker bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">speaker bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('laptop gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">laptop gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('power bank 20000mah')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">power bank 20000mah</span>
              </div>
            </div>
          )}

          {/* Tombol Lihat Lainnya untuk keyword suggestions di bagian atas */}
          <div className="main-see-more-container">
            <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
              {!showMoreSuggestions ? (
                <>
                  <i className="fas fa-plus-circle"></i>
                  <span>Lihat Lainnya</span>
                </>
              ) : (
                <>
                  <i className="fas fa-minus-circle"></i>
                  <span>Sembunyikan</span>
                </>
              )}
            </button>
          </div>

          {/* Sedang Trend section - sesuai docs/facet.html */}
          <div className="trending-section">
            <div className="trend-pill">
              Sedang Trend
              <div className="trend-pill-badge">5</div>
            </div>

            {/* Keyword suggestions - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */}
            {!showMoreSuggestions && (
              <div className="keyword-suggestions-grid">
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas')}
                >
                  tas
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('divf')}
                >
                  divf
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas mata')}
                >
                  tas mata
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('t')}
                >
                  t
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas sekolah')}
                >
                  tas sekolah
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas selempang')}
                >
                  tas selempang
                </div>
              </div>
            )}

            {/* Keyword suggestions - SETELAH klik "Lihat Lainnya" = bentuk list */}
            {showMoreSuggestions && (
              <div className="keyword-suggestions-list">
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('divf')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">divf</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas mata')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas mata</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('t')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">t</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas sekolah')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas sekolah</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas selempang')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas selempang</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas se')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas se</span>
                </div>

                {/* Additional trending items */}
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Sekolah')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Sekolah</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Selempang')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Selempang</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Handphone')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Handphone</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Mata')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Mata</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas</span>
                </div>
              </div>
            )}

            {/* Tombol Lihat Lainnya/Sembunyikan - sesuai docs/facet.html */}
            <div className="see-more-container">
              <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
                {!showMoreSuggestions ? (
                  <>
                    <i className="fas fa-plus-circle"></i>
                    <span>Lihat Lainnya</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-minus-circle"></i>
                    <span>Sembunyikan</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Product suggestions dengan keyword tags - sesuai facet.html */}
          <div className="keyword-suggestions-popup" id="keywordSuggestionsPopup">
            <div className="keyword-suggestions-title">Produk Populer</div>
            <div className="keyword-suggestions-grid">
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('iPhone 15 Pro')}
              >
                iPhone 15 Pro
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('Samsung Galaxy S24')}
              >
                Samsung Galaxy S24
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('MacBook Air M3')}
              >
                MacBook Air M3
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('AirPods Pro')}
              >
                AirPods Pro
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('iPad Pro')}
              >
                iPad Pro
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('Apple Watch')}
              >
                Apple Watch
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Keyword Predictions Container - Terpisah dari suggestions, muncul saat mengetik */}
      {showKeywordPredictions && keywordPredictions.length > 0 && (
        <div className="keyword-predictions" onClick={(e) => e.stopPropagation()}>
          {keywordPredictions.map((prediction, index) => {
            // Tentukan icon berdasarkan jenis prediksi
            let iconClass = 'fa-search'
            if (prediction.type === 'history') {
              iconClass = 'fa-history'
            } else if (prediction.type === 'product') {
              iconClass = 'fa-cart-shopping'
            } else if (prediction.type === 'trending') {
              iconClass = 'fa-arrow-trend-up'
            } else if (prediction.type === 'related') {
              iconClass = 'fa-tag'
            } else if (prediction.type === 'synonym') {
              iconClass = 'fa-exchange-alt'
            } else if (prediction.type === 'correction') {
              iconClass = 'fa-spell-check'
            }

            // Konversi teks ke Title Case untuk produk dan trending
            let displayText = prediction.text
            if (prediction.type === 'product' || prediction.type === 'trending') {
              displayText = prediction.text.split(' ').map((word: string) =>
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
              ).join(' ')
            }

            // Buat teks dengan highlight untuk bagian yang cocok
            const highlightedText = highlightMatchingText(displayText, searchValue)

            // Periksa apakah prediksi mengandung kata kunci utama dari input
            const containsMainKeyword = (input: string, predictionText: string) => {
              const inputWords = input.toLowerCase().trim().split(' ')
              if (inputWords.length > 0) {
                const mainWord = inputWords[0]
                if (mainWord.length >= 2) {
                  return predictionText.toLowerCase().includes(mainWord)
                }
              }
              return false
            }

            // Tentukan apakah ikon harus berwarna oranye
            const isRelevant = containsMainKeyword(searchValue, prediction.text)

            return (
              <div
                key={index}
                className="prediction-item"
                onClick={() => handlePredictionClick(prediction)}
              >
                <span className={`prediction-icon ${isRelevant ? 'matched' : ''}`}>
                  <i className={`fa ${iconClass}`}></i>
                </span>
                <span
                  className="prediction-text"
                  dangerouslySetInnerHTML={{ __html: highlightedText }}
                />
              </div>
            )
          })}
        </div>
      )}
      </div>
    </>
  )
}

export default SellzioPage
